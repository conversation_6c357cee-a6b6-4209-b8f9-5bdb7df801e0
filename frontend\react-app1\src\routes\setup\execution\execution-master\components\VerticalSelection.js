import React, { useState, useEffect } from 'react';
import { Card, List, Avatar, Spin, Empty, message } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import http_utils from '../../../../../util/http_utils';

const VerticalSelection = ({ selectedVertical, onVerticalSelect }) => {
    const [verticals, setVerticals] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        fetchVerticals();
    }, []);

    const fetchVerticals = async () => {
        try {
            setLoading(true);
            const response = await http_utils.get('/setup/execution/execution-master/verticals');
            
            if (response.data && response.data.success) {
                setVerticals(response.data.data || []);
            } else {
                message.error('Failed to fetch verticals');
                setVerticals([]);
            }
        } catch (error) {
            console.error('Error fetching verticals:', error);
            message.error('Error fetching verticals');
            setVerticals([]);
        } finally {
            setLoading(false);
        }
    };

    const handleVerticalClick = (vertical) => {
        onVerticalSelect(vertical);
    };

    if (loading) {
        return (
            <div className="loading-container">
                <Spin size="large" />
            </div>
        );
    }

    if (!verticals || verticals.length === 0) {
        return (
            <div className="empty-state">
                <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="No verticals found"
                />
            </div>
        );
    }

    return (
        <div className="vertical-selection-container">
            <h3>Select a Vertical</h3>
            <p>Choose a vertical to configure execution parameters for categories and SKUs.</p>
            
            <List
                grid={{
                    gutter: 16,
                    xs: 1,
                    sm: 2,
                    md: 2,
                    lg: 3,
                    xl: 3,
                    xxl: 4,
                }}
                dataSource={verticals}
                renderItem={(vertical) => (
                    <List.Item>
                        <Card
                            className={`vertical-card ${
                                selectedVertical && selectedVertical.db_id === vertical.db_id
                                    ? 'selected'
                                    : ''
                            }`}
                            hoverable
                            onClick={() => handleVerticalClick(vertical)}
                        >
                            <Card.Meta
                                avatar={
                                    <Avatar
                                        size="large"
                                        icon={<SettingOutlined />}
                                        style={{
                                            backgroundColor: vertical.is_active
                                                ? '#1890ff'
                                                : '#d9d9d9',
                                        }}
                                    />
                                }
                                title={vertical.vertical_title}
                                description={
                                    <div>
                                        <div>{vertical.vertical_desc}</div>
                                        <div style={{ marginTop: 8, fontSize: '12px', color: '#999' }}>
                                            Nature: {vertical.vertical_nature}
                                        </div>
                                        <div style={{ fontSize: '12px', color: '#999' }}>
                                            Status: {vertical.is_active ? 'Active' : 'Inactive'}
                                        </div>
                                    </div>
                                }
                            />
                        </Card>
                    </List.Item>
                )}
            />
            
            {selectedVertical && (
                <Card
                    style={{ marginTop: 20, backgroundColor: '#f6ffed', borderColor: '#b7eb8f' }}
                    size="small"
                >
                    <div>
                        <strong>Selected Vertical:</strong> {selectedVertical.vertical_title}
                    </div>
                    <div style={{ marginTop: 4, color: '#666' }}>
                        {selectedVertical.vertical_desc}
                    </div>
                </Card>
            )}
        </div>
    );
};

export default VerticalSelection;
