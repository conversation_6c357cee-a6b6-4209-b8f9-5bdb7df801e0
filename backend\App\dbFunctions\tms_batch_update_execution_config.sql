CREATE OR REPLACE FUNCTION public.tms_batch_update_execution_config(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	declare
		org_id_ int;
		usr_id_ uuid;
		ip_address_ text;
		user_agent_ text;
		batch_data json[];
		single_item json;
		
		total_processed int := 0;
		total_success int := 0;
		total_failed int := 0;
		
		status boolean := false;
		message text := 'Internal_error';
		resp_data json := '{}';
		results json[] := '{}';
	BEGIN
		-- Extract parameters
		org_id_ = (form_data_->>'org_id')::int;
		usr_id_ = (form_data_->>'usr_id')::uuid;
		ip_address_ = form_data_->>'ip_address';
		user_agent_ = form_data_->>'user_agent';
		
		-- Get batch data array
		select array(
			select json_array_elements(form_data_->'batch_data')
		) into batch_data;
		
		-- Process each item in batch
		foreach single_item in array batch_data
		loop
			begin
				total_processed := total_processed + 1;
				
				-- Add common fields to each item
				single_item := single_item || json_build_object(
					'org_id', org_id_,
					'usr_id', usr_id_,
					'ip_address', ip_address_,
					'user_agent', user_agent_
				);
				
				-- Call the single update function
				perform tms_create_or_update_execution_config(single_item);
				
				total_success := total_success + 1;
				
			exception when others then
				total_failed := total_failed + 1;
				-- Log error but continue processing
				raise notice 'Error processing item: %', SQLERRM;
			end;
		end loop;
		
		if total_success > 0 then
			status = true;
			message = 'success';
		end if;
		
		resp_data = json_build_object(
			'total_processed', total_processed,
			'total_success', total_success,
			'total_failed', total_failed
		);
		
		return json_build_object(
			'status', status,
			'message', message,
			'data', resp_data
		);
	END;
$function$;
