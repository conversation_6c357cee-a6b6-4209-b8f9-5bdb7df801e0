import React, { useState, useEffect } from 'react';
import { Card, List, Avatar, Spin, Empty, message, Tag } from 'antd';
import { ShopOutlined, ToolOutlined } from '@ant-design/icons';
import http_utils from '../../../../../util/http_utils';

const BrandServiceTypeSelection = ({ 
    verticalId, 
    selectedBrandServiceType, 
    onBrandServiceTypeSelect 
}) => {
    const [brandServiceTypes, setBrandServiceTypes] = useState([]);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (verticalId) {
            fetchBrandServiceTypes();
        } else {
            setBrandServiceTypes([]);
        }
    }, [verticalId]);

    const fetchBrandServiceTypes = async () => {
        try {
            setLoading(true);
            const response = await http_utils.get(
                `/setup/execution/execution-master/brand-service-types/${verticalId}`
            );
            
            if (response.data && response.data.success) {
                setBrandServiceTypes(response.data.data || []);
            } else {
                message.error('Failed to fetch brand-service type combinations');
                setBrandServiceTypes([]);
            }
        } catch (error) {
            console.error('Error fetching brand-service types:', error);
            message.error('Error fetching brand-service type combinations');
            setBrandServiceTypes([]);
        } finally {
            setLoading(false);
        }
    };

    const handleBrandServiceTypeClick = (brandServiceType) => {
        onBrandServiceTypeSelect(brandServiceType);
    };

    if (!verticalId) {
        return (
            <div className="empty-state">
                <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="Please select a vertical first"
                />
            </div>
        );
    }

    if (loading) {
        return (
            <div className="loading-container">
                <Spin size="large" />
            </div>
        );
    }

    if (!brandServiceTypes || brandServiceTypes.length === 0) {
        return (
            <div className="empty-state">
                <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="No brand-service type combinations found for this vertical"
                />
            </div>
        );
    }

    return (
        <div className="brand-service-type-container">
            <h3>Select Brand - Service Type Combination</h3>
            <p>Choose a brand and service type combination to configure execution parameters.</p>
            
            <List
                grid={{
                    gutter: 16,
                    xs: 1,
                    sm: 1,
                    md: 2,
                    lg: 2,
                    xl: 3,
                    xxl: 3,
                }}
                dataSource={brandServiceTypes}
                renderItem={(item) => (
                    <List.Item>
                        <Card
                            className={`brand-service-type-card ${
                                selectedBrandServiceType && 
                                selectedBrandServiceType.org_id === item.org_id &&
                                selectedBrandServiceType.service_type_id === item.service_type_id
                                    ? 'selected'
                                    : ''
                            }`}
                            hoverable
                            onClick={() => handleBrandServiceTypeClick(item)}
                        >
                            <Card.Meta
                                avatar={
                                    <Avatar
                                        size="large"
                                        icon={<ShopOutlined />}
                                        style={{
                                            backgroundColor: '#1890ff',
                                        }}
                                    />
                                }
                                title={
                                    <div>
                                        <div style={{ fontSize: '16px', fontWeight: 'bold' }}>
                                            {item.org_name}
                                        </div>
                                        <div style={{ fontSize: '14px', color: '#666', marginTop: 4 }}>
                                            <ToolOutlined style={{ marginRight: 4 }} />
                                            {item.service_type_name}
                                        </div>
                                    </div>
                                }
                                description={
                                    <div style={{ marginTop: 8 }}>
                                        <div style={{ marginBottom: 8 }}>
                                            <Tag color="blue">
                                                Org ID: {item.org_id}
                                            </Tag>
                                            <Tag color="green">
                                                Service Type ID: {item.service_type_id}
                                            </Tag>
                                        </div>
                                        {item.service_type_desc && (
                                            <div style={{ fontSize: '12px', color: '#999' }}>
                                                {item.service_type_desc}
                                            </div>
                                        )}
                                    </div>
                                }
                            />
                        </Card>
                    </List.Item>
                )}
            />
            
            {selectedBrandServiceType && (
                <Card
                    style={{ marginTop: 20, backgroundColor: '#f6ffed', borderColor: '#b7eb8f' }}
                    size="small"
                >
                    <div>
                        <strong>Selected Brand - Service Type:</strong> {selectedBrandServiceType.org_name} - {selectedBrandServiceType.service_type_name}
                    </div>
                    <div style={{ marginTop: 4, color: '#666' }}>
                        Organization ID: {selectedBrandServiceType.org_id} | Service Type ID: {selectedBrandServiceType.service_type_id}
                    </div>
                </Card>
            )}
        </div>
    );
};

export default BrandServiceTypeSelection;
