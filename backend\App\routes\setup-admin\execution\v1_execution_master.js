var express = require('express');
var router = express.Router();
const { getUserContextFrmReq } = require('../../api_models/utils/authrizor');
const model = require('../../api_models/setup_admin/execution_master_model');

function setParamsToModel(req) {
    const userContext = getUserContextFrmReq(req);
    const modelObj = new model(userContext, req.ip, req.get('User-Agent'));
    return modelObj;
}

// Get all verticals for SP
router.get('/verticals', function (req, res, next) {
    console.log('Got call for execution master verticals');
    const modelObj = setParamsToModel(req);
    modelObj.getAllVerticals(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

// Get brand-service type combinations for selected vertical
router.get('/brand-service-types/:vertical_id', function (req, res, next) {
    console.log('Got call for brand-service types');
    const modelObj = setParamsToModel(req);
    const vertical_id = req.params.vertical_id;
    modelObj.getBrandServiceTypes(req.query, vertical_id).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

// Get categories for selected brand-service type
router.get('/categories/:vertical_id/:org_id', function (req, res, next) {
    console.log('Got call for execution master categories');
    const modelObj = setParamsToModel(req);
    const { vertical_id, org_id } = req.params;
    modelObj.getCategories(req.query, vertical_id, org_id).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

// Get SKUs for selected category
router.get('/skus/:vertical_id/:org_id/:category_id', function (req, res, next) {
    console.log('Got call for execution master SKUs');
    const modelObj = setParamsToModel(req);
    const { vertical_id, org_id, category_id } = req.params;
    modelObj.getSKUs(req.query, vertical_id, org_id, category_id).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

// Update category execution config (skill, manpower, duration)
router.put('/category-config/:category_id', function (req, res, next) {
    console.log('Got call for update category execution config');
    const modelObj = setParamsToModel(req);
    const category_id = req.params.category_id;
    modelObj.updateCategoryConfig(req.body, category_id).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

// Update SKU execution config (skill, manpower, duration)
router.put('/sku-config/:sku_id', function (req, res, next) {
    console.log('Got call for update SKU execution config');
    const modelObj = setParamsToModel(req);
    const sku_id = req.params.sku_id;
    modelObj.updateSKUConfig(req.body, sku_id).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

// Get category execution config
router.get('/category-config/:category_id', function (req, res, next) {
    console.log('Got call for get category execution config');
    const modelObj = setParamsToModel(req);
    const category_id = req.params.category_id;
    modelObj.getCategoryConfig(req.query, category_id).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

// Get SKU execution config
router.get('/sku-config/:sku_id', function (req, res, next) {
    console.log('Got call for get SKU execution config');
    const modelObj = setParamsToModel(req);
    const sku_id = req.params.sku_id;
    modelObj.getSKUConfig(req.query, sku_id).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

// Batch update category configs
router.post('/category-config/batch', function (req, res, next) {
    console.log('Got call for batch update category execution config');
    const modelObj = setParamsToModel(req);
    modelObj.batchUpdateCategoryConfig(req.body).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

// Batch update SKU configs
router.post('/sku-config/batch', function (req, res, next) {
    console.log('Got call for batch update SKU execution config');
    const modelObj = setParamsToModel(req);
    modelObj.batchUpdateSKUConfig(req.body).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

module.exports = router;
