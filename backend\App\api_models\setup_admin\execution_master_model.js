const HttpStatus = require('http-status-codes');
const users_model = require('../users_model');
const sampleOperationResp = require('../utils/sampleOperationResp');

class ExecutionMasterModel {
    constructor(userContext, ip_address, user_agent_) {
        this.userContext = userContext;
        this.ip_address = ip_address;
        this.user_agent_ = user_agent_;
        this.db = userContext.db;
    }

    async fatalDbError(error) {
        console.error('Fatal DB Error:', error);
        return new sampleOperationResp(
            false,
            'Database error occurred',
            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
        );
    }

    async getAllVerticals(query) {
        try {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;

            const form_data = JSON.stringify(query);
            const dbObj = this.db;

            if (!dbObj) {
                return new sampleOperationResp(
                    false,
                    'DB not found',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }

            const res = await dbObj.tms_get_execution_master_verticals(form_data);

            return new sampleOperationResp(
                true,
                'Verticals fetched successfully',
                HttpStatus.StatusCodes.OK,
                res
            );
        } catch (error) {
            return await this.fatalDbError(error);
        }
    }

    async getBrandServiceTypes(query, vertical_id) {
        try {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;

            const form_data = JSON.stringify(query);
            const dbObj = this.db;

            if (!dbObj) {
                return new sampleOperationResp(
                    false,
                    'DB not found',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }

            const res = await dbObj.tms_get_execution_master_brand_service_types(
                form_data,
                vertical_id
            );

            return new sampleOperationResp(
                true,
                'Brand-service types fetched successfully',
                HttpStatus.StatusCodes.OK,
                res
            );
        } catch (error) {
            return await this.fatalDbError(error);
        }
    }

    async getCategories(query, vertical_id, org_id) {
        try {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;

            const form_data = JSON.stringify(query);
            const dbObj = this.db;

            if (!dbObj) {
                return new sampleOperationResp(
                    false,
                    'DB not found',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }

            const res = await dbObj.tms_get_execution_master_categories(
                form_data,
                vertical_id,
                org_id
            );

            return new sampleOperationResp(
                true,
                'Categories fetched successfully',
                HttpStatus.StatusCodes.OK,
                res
            );
        } catch (error) {
            return await this.fatalDbError(error);
        }
    }

    async getSKUs(query, vertical_id, org_id, category_id) {
        try {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;

            const form_data = JSON.stringify(query);
            const dbObj = this.db;

            if (!dbObj) {
                return new sampleOperationResp(
                    false,
                    'DB not found',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }

            const res = await dbObj.tms_get_execution_master_skus(
                form_data,
                vertical_id,
                org_id,
                category_id
            );

            return new sampleOperationResp(
                true,
                'SKUs fetched successfully',
                HttpStatus.StatusCodes.OK,
                res
            );
        } catch (error) {
            return await this.fatalDbError(error);
        }
    }

    async getCategoryConfig(query, category_id) {
        try {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;

            const form_data = JSON.stringify(query);
            const dbObj = this.db;

            if (!dbObj) {
                return new sampleOperationResp(
                    false,
                    'DB not found',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }

            const res = await dbObj.tms_get_execution_config(
                form_data,
                'CATEGORY',
                category_id
            );

            return new sampleOperationResp(
                true,
                'Category configuration fetched successfully',
                HttpStatus.StatusCodes.OK,
                res
            );
        } catch (error) {
            return await this.fatalDbError(error);
        }
    }

    async getSKUConfig(query, sku_id) {
        try {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;

            const form_data = JSON.stringify(query);
            const dbObj = this.db;

            if (!dbObj) {
                return new sampleOperationResp(
                    false,
                    'DB not found',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }

            const res = await dbObj.tms_get_execution_config(
                form_data,
                'SKU',
                sku_id
            );

            return new sampleOperationResp(
                true,
                'SKU configuration fetched successfully',
                HttpStatus.StatusCodes.OK,
                res
            );
        } catch (error) {
            return await this.fatalDbError(error);
        }
    }

    async updateCategoryConfig(query, category_id) {
        try {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['config_type'] = 'CATEGORY';
            query['reference_id'] = category_id;

            const form_data = JSON.stringify(query);
            const dbObj = this.db;

            if (!dbObj) {
                return new sampleOperationResp(
                    false,
                    'DB not found',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }

            const res = await dbObj.tms_create_or_update_execution_config(form_data);

            if (!res || !res.status) {
                return new sampleOperationResp(
                    false,
                    res?.message || 'Failed to update category configuration',
                    HttpStatus.StatusCodes.BAD_REQUEST
                );
            }

            return new sampleOperationResp(
                true,
                'Category configuration updated successfully',
                HttpStatus.StatusCodes.OK,
                res.data
            );
        } catch (error) {
            return await this.fatalDbError(error);
        }
    }

    async updateSKUConfig(query, sku_id) {
        try {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['config_type'] = 'SKU';
            query['reference_id'] = sku_id;

            const form_data = JSON.stringify(query);
            const dbObj = this.db;

            if (!dbObj) {
                return new sampleOperationResp(
                    false,
                    'DB not found',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }

            const res = await dbObj.tms_create_or_update_execution_config(form_data);

            if (!res || !res.status) {
                return new sampleOperationResp(
                    false,
                    res?.message || 'Failed to update SKU configuration',
                    HttpStatus.StatusCodes.BAD_REQUEST
                );
            }

            return new sampleOperationResp(
                true,
                'SKU configuration updated successfully',
                HttpStatus.StatusCodes.OK,
                res.data
            );
        } catch (error) {
            return await this.fatalDbError(error);
        }
    }

    async batchUpdateCategoryConfig(query) {
        try {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;

            // Add config_type to each batch item
            if (query.batch_data && Array.isArray(query.batch_data)) {
                query.batch_data = query.batch_data.map(item => ({
                    ...item,
                    config_type: 'CATEGORY',
                    reference_id: item.category_id
                }));
            }

            const form_data = JSON.stringify(query);
            const dbObj = this.db;

            if (!dbObj) {
                return new sampleOperationResp(
                    false,
                    'DB not found',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }

            const res = await dbObj.tms_batch_update_execution_config(form_data);

            if (!res || !res.status) {
                return new sampleOperationResp(
                    false,
                    res?.message || 'Failed to batch update category configurations',
                    HttpStatus.StatusCodes.BAD_REQUEST
                );
            }

            return new sampleOperationResp(
                true,
                'Category configurations updated successfully',
                HttpStatus.StatusCodes.OK,
                res.data
            );
        } catch (error) {
            return await this.fatalDbError(error);
        }
    }

    async batchUpdateSKUConfig(query) {
        try {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;

            // Add config_type to each batch item
            if (query.batch_data && Array.isArray(query.batch_data)) {
                query.batch_data = query.batch_data.map(item => ({
                    ...item,
                    config_type: 'SKU',
                    reference_id: item.sku_id
                }));
            }

            const form_data = JSON.stringify(query);
            const dbObj = this.db;

            if (!dbObj) {
                return new sampleOperationResp(
                    false,
                    'DB not found',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }

            const res = await dbObj.tms_batch_update_execution_config(form_data);

            if (!res || !res.status) {
                return new sampleOperationResp(
                    false,
                    res?.message || 'Failed to batch update SKU configurations',
                    HttpStatus.StatusCodes.BAD_REQUEST
                );
            }

            return new sampleOperationResp(
                true,
                'SKU configurations updated successfully',
                HttpStatus.StatusCodes.OK,
                res.data
            );
        } catch (error) {
            return await this.fatalDbError(error);
        }
    }
}

module.exports = ExecutionMasterModel;
