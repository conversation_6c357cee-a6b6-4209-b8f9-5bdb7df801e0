CREATE OR REPLACE FUNCTION public.tms_get_execution_master_brand_service_types(form_data_ json, vertical_id_ int)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	declare
		org_id_ int;
		resp_data json;
		srvc_type_ids int[];
	BEGIN
		org_id_ = (form_data_->>'org_id')::int;
		
		-- Get service type IDs from the vertical configuration
		select array(
			select json_array_elements_text(
				(settings.settings_data->>'srvc_type_id')::json
			)::int
		)
		from cl_tx_orgs_settings as settings
		where settings.db_id = vertical_id_
		  and settings.org_id = org_id_
		  and settings.settings_type = 'SP_CUSTOM_FIELDS'
		into srvc_type_ids;
		
		-- Get brand-service type combinations
		select json_agg(
			json_build_object(
				'org_id', orgs.db_id,
				'org_name', orgs.org_name,
				'service_type_id', st.service_type_id,
				'service_type_name', st.service_type_name,
				'service_type_desc', st.service_type_desc,
				'is_active', st.is_active
			)
		)
		from cl_cf_service_types as st
		join cl_tx_orgs as orgs on orgs.db_id = st.org_id
		where st.service_type_id = any(srvc_type_ids)
		  and st.is_active = true
		  and orgs.is_active = true
		order by orgs.org_name, st.service_type_name
		into resp_data;
		
		if resp_data is null then
			resp_data = '[]'::json;
		end if;
		
		return resp_data;
	END;
$function$;
