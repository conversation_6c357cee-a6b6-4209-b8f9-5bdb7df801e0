CREATE OR REPLACE FUNCTION public.tms_get_execution_master_verticals(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	declare
		org_id_ int;
		resp_data json;
	BEGIN
		org_id_ = (form_data_->>'org_id')::int;
		
		-- Get all verticals for the service provider organization
		select json_agg(
			json_build_object(
				'db_id', settings.db_id,
				'vertical_title', settings.settings_data->>'vertical_title',
				'vertical_desc', settings.settings_data->>'vertical_desc',
				'vertical_nature', settings.settings_data->>'vertical_nature',
				'is_active', settings.is_active,
				'created_on', (settings.c_meta).time,
				'last_updated', (settings.u_meta).time
			)
		)
		from cl_tx_orgs_settings as settings
		where settings.org_id = org_id_
		  and settings.settings_type = 'SP_CUSTOM_FIELDS'
		  and settings.is_active = true
		order by settings.settings_data->>'vertical_title'
		into resp_data;
		
		if resp_data is null then
			resp_data = '[]'::json;
		end if;
		
		return resp_data;
	END;
$function$;
