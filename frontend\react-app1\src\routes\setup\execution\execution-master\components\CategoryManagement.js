import React, { useState, useEffect } from 'react';
import { Table, Input, InputNumber, Button, Spin, Empty, message, Card, Form } from 'antd';
import { EditOutlined, CheckOutlined, CloseOutlined, SaveOutlined } from '@ant-design/icons';
import http_utils from '../../../../../util/http_utils';

const CategoryManagement = ({ verticalId, orgId, selectedCategory, onCategorySelect }) => {
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(false);
    const [editingKey, setEditingKey] = useState('');
    const [form] = Form.useForm();

    useEffect(() => {
        if (verticalId && orgId) {
            fetchCategories();
        } else {
            setCategories([]);
        }
    }, [verticalId, orgId]);

    const fetchCategories = async () => {
        try {
            setLoading(true);
            const response = await http_utils.get(
                `/setup/execution/execution-master/categories/${verticalId}/${orgId}`
            );
            
            if (response.data && response.data.success) {
                const categoriesData = response.data.data || [];
                // Fetch execution config for each category
                const categoriesWithConfig = await Promise.all(
                    categoriesData.map(async (category) => {
                        try {
                            const configResponse = await http_utils.get(
                                `/setup/execution/execution-master/category-config/${category.db_id}`
                            );
                            return {
                                ...category,
                                skill: configResponse.data?.data?.skill || '',
                                manpower: configResponse.data?.data?.manpower || 1,
                                duration: configResponse.data?.data?.duration || 60,
                            };
                        } catch (error) {
                            return {
                                ...category,
                                skill: '',
                                manpower: 1,
                                duration: 60,
                            };
                        }
                    })
                );
                setCategories(categoriesWithConfig);
            } else {
                message.error('Failed to fetch categories');
                setCategories([]);
            }
        } catch (error) {
            console.error('Error fetching categories:', error);
            message.error('Error fetching categories');
            setCategories([]);
        } finally {
            setLoading(false);
        }
    };

    const isEditing = (record) => record.db_id === editingKey;

    const edit = (record) => {
        form.setFieldsValue({
            skill: record.skill,
            manpower: record.manpower,
            duration: record.duration,
        });
        setEditingKey(record.db_id);
    };

    const cancel = () => {
        setEditingKey('');
    };

    const save = async (key) => {
        try {
            const row = await form.validateFields();
            const newData = [...categories];
            const index = newData.findIndex((item) => key === item.db_id);
            
            if (index > -1) {
                const item = newData[index];
                const updatedItem = { ...item, ...row };
                newData.splice(index, 1, updatedItem);
                
                // Save to backend
                await http_utils.put(`/setup/execution/execution-master/category-config/${key}`, {
                    skill: row.skill,
                    manpower: row.manpower,
                    duration: row.duration,
                    vertical_id: verticalId,
                    org_id: orgId,
                });
                
                setCategories(newData);
                setEditingKey('');
                message.success('Category configuration updated successfully');
            }
        } catch (error) {
            console.error('Error saving category config:', error);
            message.error('Failed to save category configuration');
        }
    };

    const saveAllChanges = async () => {
        try {
            const batchData = categories.map(category => ({
                category_id: category.db_id,
                skill: category.skill,
                manpower: category.manpower,
                duration: category.duration,
                vertical_id: verticalId,
                org_id: orgId,
            }));

            await http_utils.post('/setup/execution/execution-master/category-config/batch', {
                batch_data: batchData,
            });

            message.success('All category configurations saved successfully');
        } catch (error) {
            console.error('Error saving batch category configs:', error);
            message.error('Failed to save category configurations');
        }
    };

    const handleCategoryClick = (category) => {
        onCategorySelect(category);
    };

    const columns = [
        {
            title: 'Category Name',
            dataIndex: 'category_name',
            key: 'category_name',
            render: (text, record) => (
                <Button
                    type="link"
                    onClick={() => handleCategoryClick(record)}
                    style={{
                        padding: 0,
                        height: 'auto',
                        fontWeight: selectedCategory && selectedCategory.db_id === record.db_id ? 'bold' : 'normal',
                        color: selectedCategory && selectedCategory.db_id === record.db_id ? '#1890ff' : undefined,
                    }}
                >
                    {text}
                </Button>
            ),
        },
        {
            title: 'Description',
            dataIndex: 'category_desc',
            key: 'category_desc',
            ellipsis: true,
        },
        {
            title: 'Skill',
            dataIndex: 'skill',
            key: 'skill',
            editable: true,
            render: (text, record) => {
                const editing = isEditing(record);
                return editing ? (
                    <Form.Item
                        name="skill"
                        style={{ margin: 0 }}
                        rules={[{ required: true, message: 'Please input skill!' }]}
                    >
                        <Input />
                    </Form.Item>
                ) : (
                    text || '-'
                );
            },
        },
        {
            title: 'Manpower',
            dataIndex: 'manpower',
            key: 'manpower',
            editable: true,
            render: (text, record) => {
                const editing = isEditing(record);
                return editing ? (
                    <Form.Item
                        name="manpower"
                        style={{ margin: 0 }}
                        rules={[{ required: true, message: 'Please input manpower!' }]}
                    >
                        <InputNumber min={1} />
                    </Form.Item>
                ) : (
                    text || 1
                );
            },
        },
        {
            title: 'Duration (minutes)',
            dataIndex: 'duration',
            key: 'duration',
            editable: true,
            render: (text, record) => {
                const editing = isEditing(record);
                return editing ? (
                    <Form.Item
                        name="duration"
                        style={{ margin: 0 }}
                        rules={[{ required: true, message: 'Please input duration!' }]}
                    >
                        <InputNumber min={1} />
                    </Form.Item>
                ) : (
                    text || 60
                );
            },
        },
        {
            title: 'Action',
            key: 'action',
            render: (_, record) => {
                const editing = isEditing(record);
                return editing ? (
                    <span>
                        <Button
                            type="link"
                            onClick={() => save(record.db_id)}
                            style={{ marginRight: 8 }}
                            icon={<CheckOutlined />}
                        >
                            Save
                        </Button>
                        <Button
                            type="link"
                            onClick={cancel}
                            icon={<CloseOutlined />}
                        >
                            Cancel
                        </Button>
                    </span>
                ) : (
                    <Button
                        type="link"
                        disabled={editingKey !== ''}
                        onClick={() => edit(record)}
                        icon={<EditOutlined />}
                    >
                        Edit
                    </Button>
                );
            },
        },
    ];

    if (!verticalId || !orgId) {
        return (
            <div className="empty-state">
                <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="Please select a vertical and brand-service type combination first"
                />
            </div>
        );
    }

    if (loading) {
        return (
            <div className="loading-container">
                <Spin size="large" />
            </div>
        );
    }

    return (
        <div className="category-management-container">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                <div>
                    <h3>Category Configuration</h3>
                    <p>Configure skill, manpower, and duration for each category. Click on category name to view SKUs.</p>
                </div>
                <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={saveAllChanges}
                    disabled={editingKey !== ''}
                >
                    Save All Changes
                </Button>
            </div>

            {categories.length === 0 ? (
                <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="No categories found for this brand-service type combination"
                />
            ) : (
                <Form form={form} component={false}>
                    <Table
                        bordered
                        dataSource={categories}
                        columns={columns}
                        rowKey="db_id"
                        pagination={{
                            pageSize: 10,
                            showSizeChanger: true,
                            showQuickJumper: true,
                        }}
                        scroll={{ x: 800 }}
                    />
                </Form>
            )}

            {selectedCategory && (
                <Card
                    style={{ marginTop: 20, backgroundColor: '#f6ffed', borderColor: '#b7eb8f' }}
                    size="small"
                >
                    <div>
                        <strong>Selected Category:</strong> {selectedCategory.category_name}
                    </div>
                    <div style={{ marginTop: 4, color: '#666' }}>
                        {selectedCategory.category_desc}
                    </div>
                </Card>
            )}
        </div>
    );
};

export default CategoryManagement;
