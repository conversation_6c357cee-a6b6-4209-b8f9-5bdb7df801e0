CREATE OR REPLACE FUNCTION public.tms_get_execution_master_categories(form_data_ json, vertical_id_ int, org_id_ int)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	declare
		resp_data json;
	BEGIN
		-- Get all categories for the organization
		select json_agg(
			json_build_object(
				'db_id', cat.db_id,
				'category_name', cat.category_name,
				'category_desc', cat.category_desc,
				'is_active', cat.is_active,
				'created_on', (cat.c_meta).time,
				'last_updated', (cat.u_meta).time
			)
		)
		from cl_tx_category as cat
		where cat.org_id = org_id_
		  and cat.is_active = true
		order by cat.category_name
		into resp_data;
		
		if resp_data is null then
			resp_data = '[]'::json;
		end if;
		
		return resp_data;
	END;
$function$;
