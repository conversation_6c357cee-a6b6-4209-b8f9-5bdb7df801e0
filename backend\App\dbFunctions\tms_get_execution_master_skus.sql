CREATE OR REPLACE FUNCTION public.tms_get_execution_master_skus(form_data_ json, vertical_id_ int, org_id_ int, category_id_ int)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	declare
		resp_data json;
	BEGIN
		-- Get all SKUs for the category
		select json_agg(
			json_build_object(
				'db_id', sku.db_id,
				'sku', sku.sku,
				'product_name', sku.product_name,
				'description', sku.description,
				'price', sku.price,
				'does_has_serial_no', sku.does_has_serial_no,
				'is_active', sku.is_active,
				'category_id', category_id_,
				'created_on', (sku.c_meta).time,
				'last_updated', (sku.u_meta).time
			)
		)
		from cl_tx_product_sku as sku
		where sku.org_id = org_id_
		  and sku.form_data->>'category_id' = category_id_::text
		  and sku.is_active = true
		order by sku.product_name
		into resp_data;
		
		if resp_data is null then
			resp_data = '[]'::json;
		end if;
		
		return resp_data;
	END;
$function$;
