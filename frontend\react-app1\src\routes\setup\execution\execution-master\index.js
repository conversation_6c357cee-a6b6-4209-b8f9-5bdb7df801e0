import React, { useState, useEffect } from 'react';
import { Card, Steps, Button, message } from 'antd';
import { ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons';
import VerticalSelection from './components/VerticalSelection';
import BrandServiceTypeSelection from './components/BrandServiceTypeSelection';
import CategoryManagement from './components/CategoryManagement';
import SKUManagement from './components/SKUManagement';
import './executionMaster.css';

const { Step } = Steps;

const ExecutionMaster = () => {
    const [currentStep, setCurrentStep] = useState(0);
    const [selectedVertical, setSelectedVertical] = useState(null);
    const [selectedBrandServiceType, setSelectedBrandServiceType] = useState(null);
    const [selectedCategory, setSelectedCategory] = useState(null);

    const steps = [
        {
            title: 'Select Vertical',
            content: (
                <VerticalSelection
                    selectedVertical={selectedVertical}
                    onVerticalSelect={setSelectedVertical}
                />
            ),
        },
        {
            title: 'Select Brand - Service Type',
            content: (
                <BrandServiceTypeSelection
                    verticalId={selectedVertical?.db_id}
                    selectedBrandServiceType={selectedBrandServiceType}
                    onBrandServiceTypeSelect={setSelectedBrandServiceType}
                />
            ),
        },
        {
            title: 'Category Configuration',
            content: (
                <CategoryManagement
                    verticalId={selectedVertical?.db_id}
                    orgId={selectedBrandServiceType?.org_id}
                    selectedCategory={selectedCategory}
                    onCategorySelect={setSelectedCategory}
                />
            ),
        },
        {
            title: 'SKU Configuration',
            content: (
                <SKUManagement
                    verticalId={selectedVertical?.db_id}
                    orgId={selectedBrandServiceType?.org_id}
                    categoryId={selectedCategory?.db_id}
                />
            ),
        },
    ];

    const next = () => {
        if (currentStep === 0 && !selectedVertical) {
            message.warning('Please select a vertical to continue');
            return;
        }
        if (currentStep === 1 && !selectedBrandServiceType) {
            message.warning('Please select a brand - service type combination to continue');
            return;
        }
        if (currentStep === 2 && !selectedCategory) {
            message.warning('Please select a category to continue');
            return;
        }
        setCurrentStep(currentStep + 1);
    };

    const prev = () => {
        setCurrentStep(currentStep - 1);
    };

    const resetToStep = (step) => {
        setCurrentStep(step);
        if (step <= 0) {
            setSelectedVertical(null);
            setSelectedBrandServiceType(null);
            setSelectedCategory(null);
        } else if (step <= 1) {
            setSelectedBrandServiceType(null);
            setSelectedCategory(null);
        } else if (step <= 2) {
            setSelectedCategory(null);
        }
    };

    return (
        <div className="execution-master-container">
            <Card title="Execution Master" className="execution-master-card">
                <Steps current={currentStep} className="execution-master-steps">
                    {steps.map((item, index) => (
                        <Step
                            key={item.title}
                            title={item.title}
                            onClick={() => resetToStep(index)}
                            style={{ cursor: 'pointer' }}
                        />
                    ))}
                </Steps>

                <div className="steps-content">
                    {steps[currentStep].content}
                </div>

                <div className="steps-action">
                    {currentStep > 0 && (
                        <Button
                            style={{ margin: '0 8px' }}
                            onClick={prev}
                            icon={<ArrowLeftOutlined />}
                        >
                            Previous
                        </Button>
                    )}
                    {currentStep < steps.length - 1 && (
                        <Button
                            type="primary"
                            onClick={next}
                            icon={<ArrowRightOutlined />}
                        >
                            Next
                        </Button>
                    )}
                </div>

                {/* Display current selections */}
                <div className="current-selections">
                    {selectedVertical && (
                        <div className="selection-item">
                            <strong>Selected Vertical:</strong> {selectedVertical.vertical_title}
                        </div>
                    )}
                    {selectedBrandServiceType && (
                        <div className="selection-item">
                            <strong>Selected Brand - Service Type:</strong> {selectedBrandServiceType.org_name} - {selectedBrandServiceType.service_type_name}
                        </div>
                    )}
                    {selectedCategory && (
                        <div className="selection-item">
                            <strong>Selected Category:</strong> {selectedCategory.category_name}
                        </div>
                    )}
                </div>
            </Card>
        </div>
    );
};

export default ExecutionMaster;
