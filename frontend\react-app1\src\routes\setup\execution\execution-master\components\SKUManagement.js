import React, { useState, useEffect } from 'react';
import { Table, Input, InputNumber, Button, Spin, Empty, message, Form, Select } from 'antd';
import { EditOutlined, CheckOutlined, CloseOutlined, SaveOutlined } from '@ant-design/icons';
import http_utils from '../../../../../util/http_utils';

const { Option } = Select;

const SKUManagement = ({ verticalId, orgId, categoryId }) => {
    const [skus, setSkus] = useState([]);
    const [categories, setCategories] = useState([]);
    const [selectedCategoryId, setSelectedCategoryId] = useState(categoryId);
    const [loading, setLoading] = useState(false);
    const [editingKey, setEditingKey] = useState('');
    const [form] = Form.useForm();

    useEffect(() => {
        if (verticalId && orgId) {
            fetchCategories();
        }
    }, [verticalId, orgId]);

    useEffect(() => {
        setSelectedCategoryId(categoryId);
    }, [categoryId]);

    useEffect(() => {
        if (verticalId && orgId && selectedCategoryId) {
            fetchSKUs();
        } else {
            setSkus([]);
        }
    }, [verticalId, orgId, selectedCategoryId]);

    const fetchCategories = async () => {
        try {
            const response = await http_utils.get(
                `/setup/execution/execution-master/categories/${verticalId}/${orgId}`
            );
            
            if (response.data && response.data.success) {
                setCategories(response.data.data || []);
            }
        } catch (error) {
            console.error('Error fetching categories:', error);
        }
    };

    const fetchSKUs = async () => {
        try {
            setLoading(true);
            const response = await http_utils.get(
                `/setup/execution/execution-master/skus/${verticalId}/${orgId}/${selectedCategoryId}`
            );
            
            if (response.data && response.data.success) {
                const skusData = response.data.data || [];
                // Fetch execution config for each SKU
                const skusWithConfig = await Promise.all(
                    skusData.map(async (sku) => {
                        try {
                            const configResponse = await http_utils.get(
                                `/setup/execution/execution-master/sku-config/${sku.db_id}`
                            );
                            return {
                                ...sku,
                                skill: configResponse.data?.data?.skill || '',
                                manpower: configResponse.data?.data?.manpower || 1,
                                duration: configResponse.data?.data?.duration || 60,
                            };
                        } catch (error) {
                            return {
                                ...sku,
                                skill: '',
                                manpower: 1,
                                duration: 60,
                            };
                        }
                    })
                );
                setSkus(skusWithConfig);
            } else {
                message.error('Failed to fetch SKUs');
                setSkus([]);
            }
        } catch (error) {
            console.error('Error fetching SKUs:', error);
            message.error('Error fetching SKUs');
            setSkus([]);
        } finally {
            setLoading(false);
        }
    };

    const isEditing = (record) => record.db_id === editingKey;

    const edit = (record) => {
        form.setFieldsValue({
            skill: record.skill,
            manpower: record.manpower,
            duration: record.duration,
        });
        setEditingKey(record.db_id);
    };

    const cancel = () => {
        setEditingKey('');
    };

    const save = async (key) => {
        try {
            const row = await form.validateFields();
            const newData = [...skus];
            const index = newData.findIndex((item) => key === item.db_id);
            
            if (index > -1) {
                const item = newData[index];
                const updatedItem = { ...item, ...row };
                newData.splice(index, 1, updatedItem);
                
                // Save to backend
                await http_utils.put(`/setup/execution/execution-master/sku-config/${key}`, {
                    skill: row.skill,
                    manpower: row.manpower,
                    duration: row.duration,
                    vertical_id: verticalId,
                    org_id: orgId,
                    category_id: selectedCategoryId,
                });
                
                setSkus(newData);
                setEditingKey('');
                message.success('SKU configuration updated successfully');
            }
        } catch (error) {
            console.error('Error saving SKU config:', error);
            message.error('Failed to save SKU configuration');
        }
    };

    const saveAllChanges = async () => {
        try {
            const batchData = skus.map(sku => ({
                sku_id: sku.db_id,
                skill: sku.skill,
                manpower: sku.manpower,
                duration: sku.duration,
                vertical_id: verticalId,
                org_id: orgId,
                category_id: selectedCategoryId,
            }));

            await http_utils.post('/setup/execution/execution-master/sku-config/batch', {
                batch_data: batchData,
            });

            message.success('All SKU configurations saved successfully');
        } catch (error) {
            console.error('Error saving batch SKU configs:', error);
            message.error('Failed to save SKU configurations');
        }
    };

    const handleCategoryChange = (value) => {
        setSelectedCategoryId(value);
    };

    const columns = [
        {
            title: 'SKU Code',
            dataIndex: 'sku',
            key: 'sku',
            width: 120,
        },
        {
            title: 'Product Name',
            dataIndex: 'product_name',
            key: 'product_name',
            ellipsis: true,
        },
        {
            title: 'Description',
            dataIndex: 'description',
            key: 'description',
            ellipsis: true,
        },
        {
            title: 'Price',
            dataIndex: 'price',
            key: 'price',
            width: 100,
            render: (price) => price ? `₹${price}` : '-',
        },
        {
            title: 'Skill',
            dataIndex: 'skill',
            key: 'skill',
            width: 150,
            editable: true,
            render: (text, record) => {
                const editing = isEditing(record);
                return editing ? (
                    <Form.Item
                        name="skill"
                        style={{ margin: 0 }}
                        rules={[{ required: true, message: 'Please input skill!' }]}
                    >
                        <Input />
                    </Form.Item>
                ) : (
                    text || '-'
                );
            },
        },
        {
            title: 'Manpower',
            dataIndex: 'manpower',
            key: 'manpower',
            width: 100,
            editable: true,
            render: (text, record) => {
                const editing = isEditing(record);
                return editing ? (
                    <Form.Item
                        name="manpower"
                        style={{ margin: 0 }}
                        rules={[{ required: true, message: 'Please input manpower!' }]}
                    >
                        <InputNumber min={1} />
                    </Form.Item>
                ) : (
                    text || 1
                );
            },
        },
        {
            title: 'Duration (min)',
            dataIndex: 'duration',
            key: 'duration',
            width: 120,
            editable: true,
            render: (text, record) => {
                const editing = isEditing(record);
                return editing ? (
                    <Form.Item
                        name="duration"
                        style={{ margin: 0 }}
                        rules={[{ required: true, message: 'Please input duration!' }]}
                    >
                        <InputNumber min={1} />
                    </Form.Item>
                ) : (
                    text || 60
                );
            },
        },
        {
            title: 'Action',
            key: 'action',
            width: 120,
            render: (_, record) => {
                const editing = isEditing(record);
                return editing ? (
                    <span>
                        <Button
                            type="link"
                            onClick={() => save(record.db_id)}
                            style={{ marginRight: 8 }}
                            icon={<CheckOutlined />}
                            size="small"
                        >
                            Save
                        </Button>
                        <Button
                            type="link"
                            onClick={cancel}
                            icon={<CloseOutlined />}
                            size="small"
                        >
                            Cancel
                        </Button>
                    </span>
                ) : (
                    <Button
                        type="link"
                        disabled={editingKey !== ''}
                        onClick={() => edit(record)}
                        icon={<EditOutlined />}
                        size="small"
                    >
                        Edit
                    </Button>
                );
            },
        },
    ];

    if (!verticalId || !orgId) {
        return (
            <div className="empty-state">
                <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="Please select a vertical and brand-service type combination first"
                />
            </div>
        );
    }

    return (
        <div className="sku-management-container">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                <div>
                    <h3>SKU Configuration</h3>
                    <p>Configure skill, manpower, and duration for each SKU in the selected category.</p>
                </div>
                <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={saveAllChanges}
                    disabled={editingKey !== '' || skus.length === 0}
                >
                    Save All Changes
                </Button>
            </div>

            <div className="category-selector" style={{ marginBottom: 16 }}>
                <label style={{ marginRight: 8, fontWeight: 'bold' }}>Select Category:</label>
                <Select
                    style={{ width: 300 }}
                    placeholder="Select a category"
                    value={selectedCategoryId}
                    onChange={handleCategoryChange}
                    showSearch
                    optionFilterProp="children"
                >
                    {categories.map(category => (
                        <Option key={category.db_id} value={category.db_id}>
                            {category.category_name}
                        </Option>
                    ))}
                </Select>
            </div>

            {loading ? (
                <div className="loading-container">
                    <Spin size="large" />
                </div>
            ) : skus.length === 0 ? (
                <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description={selectedCategoryId ? "No SKUs found for this category" : "Please select a category"}
                />
            ) : (
                <Form form={form} component={false}>
                    <Table
                        bordered
                        dataSource={skus}
                        columns={columns}
                        rowKey="db_id"
                        pagination={{
                            pageSize: 10,
                            showSizeChanger: true,
                            showQuickJumper: true,
                        }}
                        scroll={{ x: 1000 }}
                        size="small"
                    />
                </Form>
            )}
        </div>
    );
};

export default SKUManagement;
