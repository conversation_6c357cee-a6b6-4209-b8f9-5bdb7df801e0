-- Create execution configuration table
CREATE TABLE IF NOT EXISTS public.cl_tx_execution_config (
    db_id SERIAL PRIMARY KEY,
    org_id INTEGER NOT NULL,
    vertical_id INTEGER,
    config_type VARCHAR(20) NOT NULL CHECK (config_type IN ('CATEGORY', 'SKU')),
    reference_id INTEGER NOT NULL, -- category_id or sku_id
    skill VARCHAR(255),
    manpower INTEGER DEFAULT 1,
    duration INTEGER DEFAULT 60, -- in minutes
    is_active BOOLEAN DEFAULT true,
    c_meta RECORD,
    c_by UUID,
    u_meta RECORD,
    u_by UUID,
    UNIQUE(org_id, config_type, reference_id)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_execution_config_org_id ON public.cl_tx_execution_config(org_id);
CREATE INDEX IF NOT EXISTS idx_execution_config_vertical_id ON public.cl_tx_execution_config(vertical_id);
CREATE INDEX IF NOT EXISTS idx_execution_config_type_ref ON public.cl_tx_execution_config(config_type, reference_id);

-- Add comments
COMMENT ON TABLE public.cl_tx_execution_config IS 'Stores execution configuration (skill, manpower, duration) for categories and SKUs';
COMMENT ON COLUMN public.cl_tx_execution_config.config_type IS 'Type of configuration: CATEGORY or SKU';
COMMENT ON COLUMN public.cl_tx_execution_config.reference_id IS 'ID of the category or SKU being configured';
COMMENT ON COLUMN public.cl_tx_execution_config.skill IS 'Required skill for execution';
COMMENT ON COLUMN public.cl_tx_execution_config.manpower IS 'Number of people required';
COMMENT ON COLUMN public.cl_tx_execution_config.duration IS 'Duration in minutes';
