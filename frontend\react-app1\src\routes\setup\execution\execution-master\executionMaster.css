.execution-master-container {
    padding: 20px;
    min-height: 100vh;
}

.execution-master-card {
    max-width: 1200px;
    margin: 0 auto;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.execution-master-steps {
    margin-bottom: 30px;
}

.execution-master-steps .ant-steps-item {
    cursor: pointer;
}

.execution-master-steps .ant-steps-item:hover .ant-steps-item-title {
    color: #1890ff;
}

.steps-content {
    min-height: 400px;
    margin: 30px 0;
    padding: 20px;
    background-color: #fafafa;
    border-radius: 6px;
}

.steps-action {
    text-align: center;
    margin: 20px 0;
}

.current-selections {
    margin-top: 20px;
    padding: 15px;
    background-color: #f0f2f5;
    border-radius: 6px;
    border-left: 4px solid #1890ff;
}

.selection-item {
    margin-bottom: 8px;
    font-size: 14px;
}

.selection-item:last-child {
    margin-bottom: 0;
}

/* Vertical Selection Styles */
.vertical-selection-container {
    padding: 20px;
}

.vertical-card {
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.vertical-card:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.vertical-card.selected {
    border-color: #1890ff;
    background-color: #f6ffed;
}

/* Brand Service Type Selection Styles */
.brand-service-type-container {
    padding: 20px;
}

.brand-service-type-card {
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.brand-service-type-card:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.brand-service-type-card.selected {
    border-color: #1890ff;
    background-color: #f6ffed;
}

/* Category Management Styles */
.category-management-container {
    padding: 20px;
}

.category-table-container {
    margin-top: 20px;
}

.editable-cell {
    position: relative;
}

.editable-cell-input-wrapper,
.editable-cell-text-wrapper {
    padding-right: 24px;
}

.editable-cell-text-wrapper {
    padding: 5px 24px 5px 5px;
}

.editable-cell-icon,
.editable-cell-icon-check,
.editable-cell-icon-cancel {
    position: absolute;
    right: 0;
    width: 20px;
    cursor: pointer;
}

.editable-cell-icon {
    line-height: 18px;
    display: none;
}

.editable-cell-icon-check,
.editable-cell-icon-cancel {
    line-height: 28px;
}

.editable-cell:hover .editable-cell-icon {
    display: inline-block;
}

.editable-cell-icon:hover,
.editable-cell-icon-check:hover,
.editable-cell-icon-cancel:hover {
    color: #108ee9;
}

.editable-cell-icon-check {
    color: #52c41a;
}

.editable-cell-icon-cancel {
    color: #ff4d4f;
}

/* SKU Management Styles */
.sku-management-container {
    padding: 20px;
}

.sku-table-container {
    margin-top: 20px;
}

.category-selector {
    margin-bottom: 20px;
}

/* Loading and Empty States */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;
}

.empty-state-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: #d9d9d9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .execution-master-container {
        padding: 10px;
    }
    
    .steps-content {
        padding: 15px;
        min-height: 300px;
    }
    
    .vertical-selection-container,
    .brand-service-type-container,
    .category-management-container,
    .sku-management-container {
        padding: 10px;
    }
}
