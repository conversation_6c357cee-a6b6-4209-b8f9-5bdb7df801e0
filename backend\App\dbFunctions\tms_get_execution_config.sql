CREATE OR REPLACE FUNCTION public.tms_get_execution_config(form_data_ json, config_type_ text, reference_id_ int)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	declare
		org_id_ int;
		resp_data json;
	BEGIN
		org_id_ = (form_data_->>'org_id')::int;
		
		-- Get execution configuration
		select json_build_object(
			'db_id', ec.db_id,
			'org_id', ec.org_id,
			'vertical_id', ec.vertical_id,
			'config_type', ec.config_type,
			'reference_id', ec.reference_id,
			'skill', ec.skill,
			'manpower', ec.manpower,
			'duration', ec.duration,
			'is_active', ec.is_active,
			'created_on', (ec.c_meta).time,
			'last_updated', (ec.u_meta).time
		)
		from cl_tx_execution_config as ec
		where ec.org_id = org_id_
		  and ec.config_type = config_type_
		  and ec.reference_id = reference_id_
		  and ec.is_active = true
		into resp_data;
		
		if resp_data is null then
			resp_data = json_build_object(
				'skill', '',
				'manpower', 1,
				'duration', 60
			);
		end if;
		
		return resp_data;
	END;
$function$;
