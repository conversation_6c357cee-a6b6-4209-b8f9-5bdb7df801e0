CREATE OR REPLACE FUNCTION public.tms_create_or_update_execution_config(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	declare
		org_id_ int;
		usr_id_ uuid;
		ip_address_ text;
		user_agent_ text;
		vertical_id_ int;
		config_type_ text;
		reference_id_ int;
		skill_ text;
		manpower_ int;
		duration_ int;
		
		existing_id int;
		ins_id int;
		affected_rows int;
		
		status boolean := false;
		message text := 'Internal_error';
		resp_data json := '{}';
	BEGIN
		-- Extract parameters
		org_id_ = (form_data_->>'org_id')::int;
		usr_id_ = (form_data_->>'usr_id')::uuid;
		ip_address_ = form_data_->>'ip_address';
		user_agent_ = form_data_->>'user_agent';
		vertical_id_ = (form_data_->>'vertical_id')::int;
		config_type_ = form_data_->>'config_type';
		reference_id_ = (form_data_->>'reference_id')::int;
		skill_ = form_data_->>'skill';
		manpower_ = (form_data_->>'manpower')::int;
		duration_ = (form_data_->>'duration')::int;
		
		-- Check if configuration already exists
		select db_id into existing_id
		from cl_tx_execution_config
		where org_id = org_id_
		  and config_type = config_type_
		  and reference_id = reference_id_;
		
		if existing_id is not null then
			-- Update existing configuration
			update cl_tx_execution_config
			set skill = skill_,
				manpower = manpower_,
				duration = duration_,
				vertical_id = vertical_id_,
				u_meta = row(ip_address_, user_agent_, now() at time zone 'utc'),
				u_by = usr_id_
			where db_id = existing_id;
			
			get diagnostics affected_rows = ROW_COUNT;
			
			if affected_rows > 0 then
				status = true;
				message = 'success';
				resp_data = json_build_object('entry_id', existing_id, 'action', 'updated');
			end if;
		else
			-- Create new configuration
			insert into cl_tx_execution_config (
				org_id, vertical_id, config_type, reference_id, skill, manpower, duration,
				is_active, c_meta, c_by
			) values (
				org_id_, vertical_id_, config_type_, reference_id_, skill_, manpower_, duration_,
				true, row(ip_address_, user_agent_, now() at time zone 'utc'), usr_id_
			) returning db_id into ins_id;
			
			get diagnostics affected_rows = ROW_COUNT;
			
			if affected_rows > 0 then
				status = true;
				message = 'success';
				resp_data = json_build_object('entry_id', ins_id, 'action', 'created');
			end if;
		end if;
		
		return json_build_object(
			'status', status,
			'message', message,
			'data', resp_data
		);
	END;
$function$;
